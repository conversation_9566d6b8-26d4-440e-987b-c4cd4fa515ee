--[[
    Yet<PERSON><PERSON><PERSON><PERSON><PERSON> - Professional Staff Time Tracker
    Database Management
]]--

YetkiliSayac.Database = {}

-- Initialize database
function YetkiliSayac.Database.Initialize()
    local prefix = YetkiliSayac.Config.Database.table_prefix

    -- Force recreate tables to ensure correct schema
    YetkiliSayac.Log("Veritabanı tabloları kontrol ediliyor...", "INFO")

    -- Drop existing tables if they have wrong schema
    sql.Query(string.format("DROP TABLE IF EXISTS %ssessions", prefix))
    sql.Query(string.format("DROP TABLE IF EXISTS %sstats", prefix))

    -- Sessions table
    local sessions_query = string.format([[
        CREATE TABLE %ssessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            steamid TEXT NOT NULL,
            player_name TEXT NOT NULL,
            job_name TEXT NOT NULL,
            start_time INTEGER NOT NULL,
            end_time INTEGER DEFAULT NULL,
            duration INTEGER DEFAULT 0,
            disconnect_reason TEXT DEFAULT NULL,
            created_at INTEGER DEFAULT (strftime('%%s', 'now'))
        )
    ]], prefix)
    
    -- Player stats table
    local stats_query = string.format([[
        CREATE TABLE %sstats (
            steamid TEXT PRIMARY KEY,
            player_name TEXT NOT NULL,
            total_time INTEGER DEFAULT 0,
            total_sessions INTEGER DEFAULT 0,
            last_job TEXT DEFAULT NULL,
            last_session_start INTEGER DEFAULT NULL,
            last_session_end INTEGER DEFAULT NULL,
            updated_at INTEGER DEFAULT (strftime('%%s', 'now'))
        )
    ]], prefix)
    
    -- Execute queries
    YetkiliSayac.Log("Sessions tablosu oluşturuluyor...", "DEBUG")
    sql.Query(sessions_query)

    local error_msg = sql.LastError()
    if error_msg and error_msg ~= "" and error_msg ~= "not an error" then
        YetkiliSayac.Log("Sessions tablo hatası: " .. tostring(error_msg), "ERROR")
        YetkiliSayac.Log("Sessions sorgusu: " .. sessions_query, "ERROR")
    else
        YetkiliSayac.Log("Sessions tablosu başarıyla oluşturuldu", "DEBUG")
    end

    YetkiliSayac.Log("Stats tablosu oluşturuluyor...", "DEBUG")
    sql.Query(stats_query)

    error_msg = sql.LastError()
    if error_msg and error_msg ~= "" and error_msg ~= "not an error" then
        YetkiliSayac.Log("Stats tablo hatası: " .. tostring(error_msg), "ERROR")
        YetkiliSayac.Log("Stats sorgusu: " .. stats_query, "ERROR")
    else
        YetkiliSayac.Log("Stats tablosu başarıyla oluşturuldu", "DEBUG")
        YetkiliSayac.Log("Veritabanı başarıyla başlatıldı")
    end
    
    -- Create indexes for better performance
    local indexes = {
        string.format("CREATE INDEX IF NOT EXISTS idx_%ssessions_steamid ON %ssessions(steamid)", prefix, prefix),
        string.format("CREATE INDEX IF NOT EXISTS idx_%ssessions_job ON %ssessions(job_name)", prefix, prefix),
        string.format("CREATE INDEX IF NOT EXISTS idx_%ssessions_time ON %ssessions(start_time)", prefix, prefix),
    }
    
    for _, index_query in ipairs(indexes) do
        sql.Query(index_query)
    end
end



-- Start a new session
function YetkiliSayac.Database.StartSession(steamid, player_name, job_name)
    local prefix = YetkiliSayac.Config.Database.table_prefix
    local start_time = os.time()

    -- Debug: Log the query
    YetkiliSayac.Log(string.format("Oturum başlatılıyor: %s (%s) - %s", player_name, steamid, job_name), "DEBUG")

    local query = string.format([[
        INSERT INTO %ssessions (steamid, player_name, job_name, start_time)
        VALUES (%s, %s, %s, %d)
    ]], prefix, sql.SQLStr(steamid), sql.SQLStr(player_name), sql.SQLStr(job_name), start_time)

    -- Debug: Log the actual query
    YetkiliSayac.Log("SQL Query: " .. query, "DEBUG")

    sql.Query(query)

    local error_msg = sql.LastError()
    if error_msg and error_msg ~= "" and error_msg ~= "not an error" then
        YetkiliSayac.Log("Oturum başlatma hatası: " .. tostring(error_msg), "ERROR")
        YetkiliSayac.Log("Hatalı sorgu: " .. query, "ERROR")
        return false
    end

    local insert_id = sql.LastInsert()
    YetkiliSayac.Log("Oturum başarıyla oluşturuldu, ID: " .. tostring(insert_id), "DEBUG")

    -- Update player stats
    YetkiliSayac.Database.UpdatePlayerStats(steamid, player_name, job_name, start_time, nil)

    return insert_id
end

-- End a session
function YetkiliSayac.Database.EndSession(steamid, disconnect_reason)
    local prefix = YetkiliSayac.Config.Database.table_prefix
    local end_time = os.time()
    
    -- Get the latest active session
    local get_session_query = string.format([[
        SELECT id, start_time FROM %ssessions 
        WHERE steamid = %s AND end_time IS NULL 
        ORDER BY start_time DESC LIMIT 1
    ]], prefix, sql.SQLStr(steamid))
    
    local session_data = sql.Query(get_session_query)
    
    if not session_data or #session_data == 0 then
        return false
    end
    
    local session_id = session_data[1].id
    local start_time = tonumber(session_data[1].start_time)
    local duration = end_time - start_time
    
    -- Only record sessions longer than minimum time
    if duration < YetkiliSayac.Config.Time.min_session_time then
        -- Delete short session
        local delete_query = string.format([[
            DELETE FROM %ssessions WHERE id = %d
        ]], prefix, session_id)
        
        sql.Query(delete_query)
        return false
    end
    
    -- Update session with end time and duration
    local update_query = string.format([[
        UPDATE %ssessions 
        SET end_time = %d, duration = %d, disconnect_reason = %s
        WHERE id = %d
    ]], prefix, end_time, duration, sql.SQLStr(disconnect_reason or "Normal"), session_id)
    
    sql.Query(update_query)
    
    local error_msg = sql.LastError()
    if error_msg then
        YetkiliSayac.Log("Oturum sonlandırma hatası: " .. error_msg, "ERROR")
        return false
    end
    
    -- Update player stats
    local player_data = sql.Query(string.format([[
        SELECT player_name, job_name FROM %ssessions WHERE id = %d
    ]], prefix, session_id))
    
    if player_data and #player_data > 0 then
        YetkiliSayac.Database.UpdatePlayerStats(steamid, player_data[1].player_name, player_data[1].job_name, start_time, end_time)
    end
    
    return duration
end

-- Update player statistics
function YetkiliSayac.Database.UpdatePlayerStats(steamid, player_name, job_name, start_time, end_time)
    local prefix = YetkiliSayac.Config.Database.table_prefix
    
    -- Get current stats
    local stats_query = string.format([[
        SELECT total_time, total_sessions FROM %sstats WHERE steamid = %s
    ]], prefix, sql.SQLStr(steamid))
    
    local stats_data = sql.Query(stats_query)
    local total_time = 0
    local total_sessions = 0
    
    if stats_data and #stats_data > 0 then
        total_time = tonumber(stats_data[1].total_time) or 0
        total_sessions = tonumber(stats_data[1].total_sessions) or 0
    end
    
    -- Calculate new values
    if end_time then
        local duration = end_time - start_time
        total_time = total_time + duration
        total_sessions = total_sessions + 1
    end
    
    -- Upsert player stats
    local upsert_query = string.format([[
        INSERT OR REPLACE INTO %sstats 
        (steamid, player_name, total_time, total_sessions, last_job, last_session_start, last_session_end, updated_at)
        VALUES (%s, %s, %d, %d, %s, %d, %s, %d)
    ]], prefix, 
        sql.SQLStr(steamid), 
        sql.SQLStr(player_name), 
        total_time, 
        total_sessions, 
        sql.SQLStr(job_name),
        start_time,
        end_time and tostring(end_time) or "NULL",
        os.time()
    )
    
    sql.Query(upsert_query)
    
    local error_msg = sql.LastError()
    if error_msg then
        YetkiliSayac.Log("İstatistik güncelleme hatası: " .. error_msg, "ERROR")
    end
end

-- Get player statistics
function YetkiliSayac.Database.GetPlayerStats(steamid)
    local prefix = YetkiliSayac.Config.Database.table_prefix

    local query = string.format([[
        SELECT * FROM %sstats WHERE steamid = %s
    ]], prefix, sql.SQLStr(steamid))

    local result = sql.Query(query)

    if result and #result > 0 then
        return result[1]
    end

    return nil
end

-- Get player sessions
function YetkiliSayac.Database.GetPlayerSessions(steamid, limit)
    local prefix = YetkiliSayac.Config.Database.table_prefix
    limit = limit or 50

    local query = string.format([[
        SELECT * FROM %ssessions
        WHERE steamid = %s
        ORDER BY start_time DESC
        LIMIT %d
    ]], prefix, sql.SQLStr(steamid), limit)

    return sql.Query(query) or {}
end

-- Get all player statistics (for admin panel)
function YetkiliSayac.Database.GetAllStats(limit)
    local prefix = YetkiliSayac.Config.Database.table_prefix
    limit = limit or 100

    local query = string.format([[
        SELECT * FROM %sstats
        ORDER BY total_time DESC
        LIMIT %d
    ]], prefix, limit)

    return sql.Query(query) or {}
end

-- Get all sessions (for admin panel)
function YetkiliSayac.Database.GetAllSessions(limit)
    local prefix = YetkiliSayac.Config.Database.table_prefix
    limit = limit or 200

    local query = string.format([[
        SELECT * FROM %ssessions
        ORDER BY start_time DESC
        LIMIT %d
    ]], prefix, limit)

    return sql.Query(query) or {}
end

-- Reset player statistics
function YetkiliSayac.Database.ResetPlayerStats(steamid)
    local prefix = YetkiliSayac.Config.Database.table_prefix

    -- Delete all sessions
    local delete_sessions = string.format([[
        DELETE FROM %ssessions WHERE steamid = %s
    ]], prefix, sql.SQLStr(steamid))

    -- Delete stats
    local delete_stats = string.format([[
        DELETE FROM %sstats WHERE steamid = %s
    ]], prefix, sql.SQLStr(steamid))

    sql.Query(delete_sessions)
    sql.Query(delete_stats)

    local error_msg = sql.LastError()
    if error_msg then
        YetkiliSayac.Log("İstatistik sıfırlama hatası: " .. error_msg, "ERROR")
        return false
    end

    return true
end

-- Cleanup old records
function YetkiliSayac.Database.CleanupOldRecords()
    local prefix = YetkiliSayac.Config.Database.table_prefix
    local cleanup_time = os.time() - (YetkiliSayac.Config.Database.auto_cleanup_days * 24 * 60 * 60)

    local query = string.format([[
        DELETE FROM %ssessions WHERE start_time < %d
    ]], prefix, cleanup_time)

    sql.Query(query)

    local error_msg = sql.LastError()
    if error_msg then
        YetkiliSayac.Log("Eski kayıt temizleme hatası: " .. error_msg, "ERROR")
    else
        YetkiliSayac.Log("Eski kayıtlar temizlendi")
    end
end

--[[
    Yetki<PERSON><PERSON>ayac - Professional Staff Time Tracker
    Core Server Logic
]]--

YetkiliSayac.ActiveSessions = YetkiliSayac.ActiveSessions or {}

-- Start a session for a player
function YetkiliSayac.StartSession(ply, job_name)
    if not IsValid(ply) then return false end
    
    local steamid = ply:SteamID()
    local player_name = ply:<PERSON>()
    
    -- Check if player already has an active session
    if YetkiliSayac.ActiveSessions[steamid] then
        YetkiliSayac.Log(player_name .. " zaten aktif bir oturuma sahip", "WARNING")
        return false
    end
    
    -- Check if job is a staff job
    local isStaff, config = YetkiliSayac.IsStaffJob(job_name)
    if not isStaff then
        return false
    end
    
    -- Start database session
    local session_id = YetkiliSayac.Database.StartSession(steamid, player_name, job_name)
    if not session_id then
        YetkiliSayac.Log("Veritabanı oturumu başlatılamadı: " .. player_name, "ERROR")
        return false
    end
    
    -- Store active session
    YetkiliSayac.ActiveSessions[steamid] = {
        session_id = session_id,
        player_name = player_name,
        job_name = job_name,
        start_time = os.time(),
        last_update = os.time()
    }
    
    -- Send Discord notification
    YetkiliSayac.Discord.SendJobStart(ply, job_name)
    
    -- Notify player
    ply:ChatPrint(string.format("[YetkiliSayac] %s görevine başladınız. Süre sayımı başlatıldı!", job_name))
    
    YetkiliSayac.Log(string.format("%s (%s) %s görevine başladı", player_name, steamid, job_name))
    
    return true
end

-- End a session for a player
function YetkiliSayac.EndSession(ply, reason)
    local steamid = ply and ply:SteamID() or nil
    if not steamid then return false end
    
    local session = YetkiliSayac.ActiveSessions[steamid]
    if not session then
        return false
    end
    
    -- End database session
    local duration = YetkiliSayac.Database.EndSession(steamid, reason)
    if not duration then
        YetkiliSayac.Log("Veritabanı oturumu sonlandırılamadı: " .. steamid, "ERROR")
    end
    
    -- Send Discord notification
    if duration and duration > 0 then
        YetkiliSayac.Discord.SendJobEnd(ply, session.job_name, duration, reason)
        
        -- Notify player if still connected
        if IsValid(ply) then
            ply:ChatPrint(string.format("[YetkiliSayac] %s görevinden ayrıldınız. Toplam süre: %s", 
                session.job_name, YetkiliSayac.FormatTime(duration)))
        end
        
        YetkiliSayac.Log(string.format("%s (%s) %s görevinden ayrıldı. Süre: %s", 
            session.player_name, steamid, session.job_name, YetkiliSayac.FormatTime(duration)))
    end
    
    -- Remove active session
    YetkiliSayac.ActiveSessions[steamid] = nil
    
    return duration or 0
end

-- DarkRP job change hook
hook.Add("playerChangedJob", "YetkiliSayac_JobChange", function(ply, old_job, new_job)
    if not IsValid(ply) then return end
    
    local steamid = ply:SteamID()
    local old_is_staff = YetkiliSayac.IsStaffJob(old_job)
    local new_is_staff = YetkiliSayac.IsStaffJob(new_job)
    
    -- End old session if it was a staff job
    if old_is_staff and YetkiliSayac.ActiveSessions[steamid] then
        YetkiliSayac.EndSession(ply, "Meslek değişikliği")
    end
    
    -- Start new session if it's a staff job
    if new_is_staff then
        timer.Simple(1, function()
            if IsValid(ply) then
                YetkiliSayac.StartSession(ply, new_job)
            end
        end)
    end
end)

-- Update active sessions periodically
timer.Create("YetkiliSayac_UpdateSessions", YetkiliSayac.Config.Time.update_interval, 0, function()
    local current_time = os.time()
    
    for steamid, session in pairs(YetkiliSayac.ActiveSessions) do
        local ply = YetkiliSayac.GetPlayerBySteamID(steamid)
        
        -- Check if player is still connected
        if not IsValid(ply) then
            YetkiliSayac.ActiveSessions[steamid] = nil
            continue
        end
        
        -- Check if player is still in the same job
        local current_job = YetkiliSayac.GetPlayerJob(ply)
        if current_job ~= session.job_name then
            YetkiliSayac.EndSession(ply, "Meslek değişikliği")
            
            -- Start new session if new job is also staff
            local is_staff = YetkiliSayac.IsStaffJob(current_job)
            if is_staff then
                YetkiliSayac.StartSession(ply, current_job)
            end
            continue
        end
        
        -- Update last activity time
        session.last_update = current_time
    end
end)

-- Network handlers
net.Receive("YetkiliSayac_RequestStats", function(len, ply)
    if not IsValid(ply) then return end
    
    if not YetkiliSayac.HasPermission(ply, "view_own_stats") then
        return
    end
    
    local steamid = ply:SteamID()
    local stats = YetkiliSayac.Database.GetPlayerStats(steamid)
    local sessions = YetkiliSayac.Database.GetPlayerSessions(steamid, 20)
    local active_session = YetkiliSayac.ActiveSessions[steamid]
    
    local data = {
        stats = stats,
        sessions = sessions,
        active_session = active_session
    }
    
    net.Start("YetkiliSayac_SendStats")
    net.WriteTable(data)
    net.Send(ply)
end)

net.Receive("YetkiliSayac_RequestAllSessions", function(len, ply)
    if not IsValid(ply) then return end
    
    if not YetkiliSayac.HasPermission(ply, "view_all_stats") then
        return
    end
    
    local all_stats = YetkiliSayac.Database.GetAllStats(50)
    local all_sessions = YetkiliSayac.Database.GetAllSessions(100)
    
    local data = {
        stats = all_stats,
        sessions = all_sessions,
        active_sessions = YetkiliSayac.ActiveSessions
    }
    
    net.Start("YetkiliSayac_SendAllSessions")
    net.WriteTable(data)
    net.Send(ply)
end)

net.Receive("YetkiliSayac_RequestAdminData", function(len, ply)
    if not IsValid(ply) then return end
    
    if not YetkiliSayac.HasPermission(ply, "manage_system") then
        return
    end
    
    local target_steamid = net.ReadString()
    
    if not YetkiliSayac.IsValidSteamID(target_steamid) then
        return
    end
    
    local stats = YetkiliSayac.Database.GetPlayerStats(target_steamid)
    local sessions = YetkiliSayac.Database.GetPlayerSessions(target_steamid, 50)
    
    local data = {
        steamid = target_steamid,
        stats = stats,
        sessions = sessions
    }
    
    net.Start("YetkiliSayac_SendAdminData")
    net.WriteTable(data)
    net.Send(ply)
end)

net.Receive("YetkiliSayac_ResetPlayerStats", function(len, ply)
    if not IsValid(ply) then return end
    
    if not YetkiliSayac.HasPermission(ply, "reset_stats") then
        return
    end
    
    local target_steamid = net.ReadString()
    
    if not YetkiliSayac.IsValidSteamID(target_steamid) then
        return
    end
    
    -- End active session if exists
    if YetkiliSayac.ActiveSessions[target_steamid] then
        local target_ply = YetkiliSayac.GetPlayerBySteamID(target_steamid)
        YetkiliSayac.EndSession(target_ply, "İstatistik sıfırlama")
    end
    
    -- Reset database stats
    local success = YetkiliSayac.Database.ResetPlayerStats(target_steamid)
    
    if success then
        ply:ChatPrint("[YetkiliSayac] Oyuncu istatistikleri başarıyla sıfırlandı!")
        YetkiliSayac.Log(string.format("%s (%s) tarafından %s oyuncusunun istatistikleri sıfırlandı", 
            ply:Nick(), ply:SteamID(), target_steamid))
    else
        ply:ChatPrint("[YetkiliSayac] İstatistik sıfırlama başarısız!")
    end
end)

-- Cleanup timer
timer.Create("YetkiliSayac_Cleanup", 24 * 60 * 60, 0, function() -- Daily cleanup
    YetkiliSayac.Database.CleanupOldRecords()
end)

-- Daily report timer
timer.Create("YetkiliSayac_DailyReport", 24 * 60 * 60, 0, function()
    YetkiliSayac.Discord.SendDailyReport()
end)

-- Weekly report timer (every Sunday at midnight)
timer.Create("YetkiliSayac_WeeklyReport", 7 * 24 * 60 * 60, 0, function()
    local day_of_week = tonumber(os.date("%w"))
    if day_of_week == 0 then -- Sunday
        YetkiliSayac.Discord.SendWeeklyReport()
    end
end)

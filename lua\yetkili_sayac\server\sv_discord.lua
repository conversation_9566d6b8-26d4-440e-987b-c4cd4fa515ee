--[[
    Yetki<PERSON><PERSON><PERSON><PERSON> - Professional Staff Time Tracker
    Discord Integration
]]--

YetkiliSayac.Discord = {}

-- Send Discord webhook
function YetkiliSayac.Discord.SendWebhook(data)
    if not YetkiliSayac.Config.Discord.enabled or not YetkiliSayac.Config.Discord.webhook_url then
        return
    end
    
    if YetkiliSayac.Config.Discord.webhook_url == "YOUR_DISCORD_WEBHOOK_URL_HERE" then
        YetkiliSayac.Log("Discord webhook URL ayarlanmamış!", "WARNING")
        return
    end
    
    local webhook_data = {
        username = YetkiliSayac.Config.Discord.bot_name,
        avatar_url = YetkiliSayac.Config.Discord.avatar_url,
        embeds = {data}
    }
    
    local json_data = util.TableToJSON(webhook_data)
    
    HTTP({
        url = YetkiliSayac.Config.Discord.webhook_url,
        method = "POST",
        headers = {
            ["Content-Type"] = "application/json"
        },
        body = json_data,
        success = function(code, body, headers)
            YetkiliSayac.Log("Discord webhook başarıyla gönderildi")
        end,
        failed = function(reason)
            YetkiliSayac.Log("Discord webhook hatası: " .. reason, "ERROR")
        end
    })
end

-- Send job start notification
function YetkiliSayac.Discord.SendJobStart(ply, job_name)
    local isStaff, config = YetkiliSayac.IsStaffJob(job_name)
    if not isStaff then return end
    
    local embed = {
        title = "🟢 Yetkili Göreve Başladı",
        description = string.format("**%s** yetkili görevine başladı", ply:Nick()),
        color = config.discord_color or YetkiliSayac.Config.Discord.color,
        fields = {
            {
                name = "👤 Oyuncu",
                value = ply:Nick(),
                inline = true
            },
            {
                name = "🎯 Görev",
                value = job_name,
                inline = true
            },
            {
                name = "🆔 SteamID",
                value = ply:SteamID(),
                inline = true
            },
            {
                name = "⏰ Başlangıç Zamanı",
                value = os.date("%d/%m/%Y %H:%M:%S"),
                inline = false
            }
        },
        footer = {
            text = "YetkiliSayac Sistemi",
            icon_url = YetkiliSayac.Config.Discord.avatar_url
        },
        timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
    }
    
    if YetkiliSayac.Config.Discord.mention_role and YetkiliSayac.Config.Discord.mention_role ~= "" then
        embed.content = string.format("<@&%s>", YetkiliSayac.Config.Discord.mention_role)
    end
    
    YetkiliSayac.Discord.SendWebhook(embed)
end

-- Send job end notification
function YetkiliSayac.Discord.SendJobEnd(ply, job_name, duration, reason)
    local isStaff, config = YetkiliSayac.IsStaffJob(job_name)
    if not isStaff then return end
    
    local formatted_duration = YetkiliSayac.FormatTime(duration)
    local color = config.discord_color or YetkiliSayac.Config.Discord.color
    
    -- Change color based on duration
    if duration < 300 then -- Less than 5 minutes
        color = 16711680 -- Red
    elseif duration < 1800 then -- Less than 30 minutes
        color = 16753920 -- Orange
    else
        color = 65280 -- Green
    end
    
    local embed = {
        title = "🔴 Yetkili Görevden Ayrıldı",
        description = string.format("**%s** yetkili görevinden ayrıldı", ply and ply:Nick() or "Bilinmeyen Oyuncu"),
        color = color,
        fields = {
            {
                name = "👤 Oyuncu",
                value = ply and ply:Nick() or "Bilinmeyen Oyuncu",
                inline = true
            },
            {
                name = "🎯 Görev",
                value = job_name,
                inline = true
            },
            {
                name = "⏱️ Görev Süresi",
                value = formatted_duration,
                inline = true
            },
            {
                name = "📝 Ayrılma Sebebi",
                value = reason or "Bilinmiyor",
                inline = false
            },
            {
                name = "⏰ Bitiş Zamanı",
                value = os.date("%d/%m/%Y %H:%M:%S"),
                inline = false
            }
        },
        footer = {
            text = "YetkiliSayac Sistemi",
            icon_url = YetkiliSayac.Config.Discord.avatar_url
        },
        timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
    }
    
    YetkiliSayac.Discord.SendWebhook(embed)
end

-- Send daily report
function YetkiliSayac.Discord.SendDailyReport()
    local today_start = os.time() - (os.time() % 86400)
    local prefix = YetkiliSayac.Config.Database.table_prefix
    
    local query = string.format([[
        SELECT job_name, COUNT(*) as session_count, SUM(duration) as total_duration
        FROM %ssessions 
        WHERE start_time >= %d AND end_time IS NOT NULL
        GROUP BY job_name
        ORDER BY total_duration DESC
    ]], prefix, today_start)
    
    local results = sql.Query(query)
    
    if not results or #results == 0 then
        return
    end
    
    local fields = {}
    local total_time = 0
    
    for _, row in ipairs(results) do
        local duration = tonumber(row.total_duration) or 0
        total_time = total_time + duration
        
        table.insert(fields, {
            name = row.job_name,
            value = string.format("**%d** oturum - **%s**", 
                tonumber(row.session_count) or 0, 
                YetkiliSayac.FormatTime(duration)
            ),
            inline = true
        })
    end
    
    local embed = {
        title = "📊 Günlük Yetkili Raporu",
        description = string.format("**%s** tarihli yetkili aktivite raporu", os.date("%d/%m/%Y")),
        color = YetkiliSayac.Config.Discord.color,
        fields = fields,
        footer = {
            text = string.format("Toplam Aktif Süre: %s | YetkiliSayac Sistemi", YetkiliSayac.FormatTime(total_time)),
            icon_url = YetkiliSayac.Config.Discord.avatar_url
        },
        timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
    }
    
    YetkiliSayac.Discord.SendWebhook(embed)
end

-- Send weekly report
function YetkiliSayac.Discord.SendWeeklyReport()
    local week_start = os.time() - (7 * 24 * 60 * 60)
    local prefix = YetkiliSayac.Config.Database.table_prefix
    
    local query = string.format([[
        SELECT s.steamid, s.player_name, s.job_name, 
               COUNT(*) as session_count, SUM(s.duration) as total_duration
        FROM %ssessions s
        WHERE s.start_time >= %d AND s.end_time IS NOT NULL
        GROUP BY s.steamid, s.job_name
        ORDER BY total_duration DESC
        LIMIT 10
    ]], prefix, week_start)
    
    local results = sql.Query(query)
    
    if not results or #results == 0 then
        return
    end
    
    local fields = {}
    
    for i, row in ipairs(results) do
        local duration = tonumber(row.total_duration) or 0
        
        table.insert(fields, {
            name = string.format("#%d - %s (%s)", i, row.player_name, row.job_name),
            value = string.format("**%d** oturum - **%s**", 
                tonumber(row.session_count) or 0, 
                YetkiliSayac.FormatTime(duration)
            ),
            inline = false
        })
    end
    
    local embed = {
        title = "📈 Haftalık Yetkili Raporu",
        description = "Son 7 günün en aktif yetkilileri",
        color = YetkiliSayac.Config.Discord.color,
        fields = fields,
        footer = {
            text = "YetkiliSayac Sistemi",
            icon_url = YetkiliSayac.Config.Discord.avatar_url
        },
        timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
    }
    
    YetkiliSayac.Discord.SendWebhook(embed)
end

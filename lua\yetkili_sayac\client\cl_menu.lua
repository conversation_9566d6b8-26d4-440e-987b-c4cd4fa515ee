--[[
    Yetki<PERSON><PERSON>ayac - Professional Staff Time Tracker
    Client Menu System
]]--

include("yetkili_sayac/shared/sh_config.lua")
include("yetkili_sayac/shared/sh_functions.lua")

YetkiliSayac.Menu = {}
YetkiliSayac.Menu.IsOpen = false
YetkiliSayac.Menu.CurrentTab = 1
YetkiliSayac.Menu.Data = {}
YetkiliSayac.Menu.AnimationStart = 0
YetkiliSayac.Menu.ScrollY = 0

-- Fonts
surface.CreateFont("YetkiliSayac_Title", {
    font = "Roboto",
    size = 28,
    weight = 600,
    antialias = true,
})

surface.CreateFont("YetkiliSayac_Header", {
    font = "Roboto",
    size = 20,
    weight = 500,
    antialias = true,
})

surface.CreateFont("YetkiliSayac_Text", {
    font = "Roboto",
    size = 16,
    weight = 400,
    antialias = true,
})

surface.CreateFont("YetkiliSayac_Small", {
    font = "Roboto",
    size = 14,
    weight = 400,
    antialias = true,
})

-- Open menu
function YetkiliSayac.Menu.Open()
    if YetkiliSayac.Menu.IsOpen then
        print("[YetkiliSayac] Menü zaten açık")
        return
    end

    print("[YetkiliSayac] Menü açılıyor...")
    YetkiliSayac.Menu.IsOpen = true
    YetkiliSayac.Menu.AnimationStart = CurTime()
    YetkiliSayac.Menu.CurrentTab = 1
    YetkiliSayac.Menu.ScrollY = 0

    -- Request initial data
    net.Start("YetkiliSayac_RequestStats")
    net.SendToServer()

    gui.EnableScreenClicker(true)
    print("[YetkiliSayac] Menü açıldı")
end

-- Close menu
function YetkiliSayac.Menu.Close()
    if not YetkiliSayac.Menu.IsOpen then return end
    
    YetkiliSayac.Menu.IsOpen = false
    gui.EnableScreenClicker(false)
end

-- Draw rounded rectangle with gradient
function YetkiliSayac.Menu.DrawGradientBox(x, y, w, h, color1, color2, radius)
    radius = radius or 8
    
    -- Draw gradient background
    local steps = 20
    for i = 0, steps do
        local frac = i / steps
        local color = YetkiliSayac.LerpColor(frac, color1, color2)
        local step_h = h / steps
        
        draw.RoundedBox(0, x, y + i * step_h, w, step_h + 1, color)
    end
    
    -- Draw rounded overlay
    YetkiliSayac.DrawRoundedBox(radius, x, y, w, h, Color(0, 0, 0, 0))
end

-- Draw tab button
function YetkiliSayac.Menu.DrawTabButton(x, y, w, h, text, active, icon)
    local color = active and YetkiliSayac.Config.Colors.primary or YetkiliSayac.Config.Colors.dark
    local text_color = active and YetkiliSayac.Config.Colors.white or YetkiliSayac.Config.Colors.light
    
    -- Hover effect
    local mx, my = gui.MouseX(), gui.MouseY()
    local hovered = mx >= x and mx <= x + w and my >= y and my <= y + h
    
    if hovered and not active then
        color = YetkiliSayac.LerpColor(0.3, color, YetkiliSayac.Config.Colors.primary)
    end
    
    YetkiliSayac.DrawRoundedBox(6, x, y, w, h, color)
    
    -- Draw icon if provided
    if icon then
        YetkiliSayac.DrawTextShadow(icon, "YetkiliSayac_Text", x + 10, y + h/2, text_color, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
        YetkiliSayac.DrawTextShadow(text, "YetkiliSayac_Text", x + 35, y + h/2, text_color, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
    else
        YetkiliSayac.DrawTextShadow(text, "YetkiliSayac_Text", x + w/2, y + h/2, text_color, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    
    return hovered
end

-- Draw stat card
function YetkiliSayac.Menu.DrawStatCard(x, y, w, h, title, value, icon, color)
    color = color or YetkiliSayac.Config.Colors.primary
    
    -- Card background
    YetkiliSayac.DrawRoundedBox(8, x, y, w, h, Color(255, 255, 255, 250))
    YetkiliSayac.DrawRoundedBox(8, x, y, w, 4, color)
    
    -- Icon
    if icon then
        YetkiliSayac.DrawTextShadow(icon, "YetkiliSayac_Header", x + 15, y + 25, color, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
    end
    
    -- Title
    YetkiliSayac.DrawTextShadow(title, "YetkiliSayac_Small", x + (icon and 45 or 15), y + 15, YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
    
    -- Value
    YetkiliSayac.DrawTextShadow(value, "YetkiliSayac_Text", x + (icon and 45 or 15), y + 35, YetkiliSayac.Config.Colors.black, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
end

-- Draw session list item
function YetkiliSayac.Menu.DrawSessionItem(x, y, w, h, session, index)
    local bg_color = index % 2 == 0 and Color(248, 249, 250) or Color(255, 255, 255)
    
    YetkiliSayac.DrawRoundedBox(4, x, y, w, h, bg_color)
    
    -- Job name
    local job_color = YetkiliSayac.Config.Colors.primary
    if YetkiliSayac.Config.StaffJobs[session.job_name] then
        job_color = YetkiliSayac.Config.StaffJobs[session.job_name].color or job_color
    end
    
    YetkiliSayac.DrawTextShadow(session.job_name, "YetkiliSayac_Text", x + 15, y + 10, job_color, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
    
    -- Duration
    local duration = tonumber(session.duration) or 0
    local duration_text = YetkiliSayac.FormatTime(duration)
    YetkiliSayac.DrawTextShadow(duration_text, "YetkiliSayac_Small", x + w - 15, y + 10, YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_RIGHT, TEXT_ALIGN_TOP)
    
    -- Start time
    local start_time = os.date("%d/%m/%Y %H:%M", tonumber(session.start_time) or 0)
    YetkiliSayac.DrawTextShadow(start_time, "YetkiliSayac_Small", x + 15, y + 30, YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
    
    -- Status
    local status = session.end_time and "Tamamlandı" or "Devam ediyor"
    local status_color = session.end_time and YetkiliSayac.Config.Colors.success or YetkiliSayac.Config.Colors.warning
    YetkiliSayac.DrawTextShadow(status, "YetkiliSayac_Small", x + w - 15, y + 30, status_color, TEXT_ALIGN_RIGHT, TEXT_ALIGN_TOP)
end

-- Draw my stats tab
function YetkiliSayac.Menu.DrawMyStatsTab(x, y, w, h)
    local stats = YetkiliSayac.Menu.Data.stats
    local sessions = YetkiliSayac.Menu.Data.sessions or {}
    local active_session = YetkiliSayac.Menu.Data.active_session
    
    local card_w = (w - 45) / 3
    local card_h = 80
    local start_y = y + 20
    
    if stats then
        -- Total time card
        YetkiliSayac.Menu.DrawStatCard(x + 15, start_y, card_w, card_h, 
            "Toplam Süre", YetkiliSayac.FormatTime(tonumber(stats.total_time) or 0), 
            "⏱️", YetkiliSayac.Config.Colors.primary)
        
        -- Total sessions card
        YetkiliSayac.Menu.DrawStatCard(x + 30 + card_w, start_y, card_w, card_h, 
            "Toplam Oturum", tostring(tonumber(stats.total_sessions) or 0), 
            "📊", YetkiliSayac.Config.Colors.success)
        
        -- Last job card
        YetkiliSayac.Menu.DrawStatCard(x + 45 + card_w * 2, start_y, card_w, card_h, 
            "Son Görev", stats.last_job or "Yok", 
            "🎯", YetkiliSayac.Config.Colors.warning)
    else
        YetkiliSayac.DrawTextShadow("Veri yükleniyor...", "YetkiliSayac_Text", x + w/2, start_y + 40, 
            YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    
    -- Active session info
    start_y = start_y + card_h + 30
    
    if active_session then
        YetkiliSayac.DrawRoundedBox(8, x + 15, start_y, w - 30, 60, Color(46, 204, 113, 20))
        YetkiliSayac.DrawTextShadow("🟢 Aktif Oturum", "YetkiliSayac_Header", x + 30, start_y + 15, 
            YetkiliSayac.Config.Colors.success, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
        
        local current_duration = os.time() - (active_session.start_time or 0)
        YetkiliSayac.DrawTextShadow(active_session.job_name .. " - " .. YetkiliSayac.FormatTime(current_duration), 
            "YetkiliSayac_Text", x + 30, start_y + 35, YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
    else
        YetkiliSayac.DrawRoundedBox(8, x + 15, start_y, w - 30, 60, Color(231, 76, 60, 20))
        YetkiliSayac.DrawTextShadow("🔴 Aktif Oturum Yok", "YetkiliSayac_Header", x + 30, start_y + 20, 
            YetkiliSayac.Config.Colors.danger, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
    end
    
    -- Recent sessions
    start_y = start_y + 80
    YetkiliSayac.DrawTextShadow("Son Oturumlar", "YetkiliSayac_Header", x + 15, start_y, 
        YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
    
    start_y = start_y + 35
    local session_h = 55
    local visible_sessions = math.floor((h - (start_y - y)) / session_h)
    
    if #sessions > 0 then
        for i = 1, math.min(#sessions, visible_sessions) do
            YetkiliSayac.Menu.DrawSessionItem(x + 15, start_y + (i-1) * session_h, w - 30, session_h - 5, sessions[i], i)
        end
    else
        YetkiliSayac.DrawTextShadow("Henüz oturum kaydı bulunmuyor", "YetkiliSayac_Text", x + w/2, start_y + 50,
            YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
end

-- Draw all sessions tab
function YetkiliSayac.Menu.DrawAllSessionsTab(x, y, w, h)
    local all_data = YetkiliSayac.Menu.Data.all_sessions or {}
    local stats = all_data.stats or {}
    local sessions = all_data.sessions or {}

    -- Top players section
    YetkiliSayac.DrawTextShadow("En Aktif Yetkililer", "YetkiliSayac_Header", x + 15, y + 20,
        YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)

    local start_y = y + 55
    local player_h = 45
    local visible_players = math.min(#stats, 8)

    for i = 1, visible_players do
        local stat = stats[i]
        local bg_color = i % 2 == 0 and Color(248, 249, 250) or Color(255, 255, 255)

        YetkiliSayac.DrawRoundedBox(4, x + 15, start_y + (i-1) * player_h, w - 30, player_h - 5, bg_color)

        -- Rank
        local rank_color = i <= 3 and YetkiliSayac.Config.Colors.warning or YetkiliSayac.Config.Colors.dark
        YetkiliSayac.DrawTextShadow("#" .. i, "YetkiliSayac_Text", x + 30, start_y + (i-1) * player_h + 15,
            rank_color, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

        -- Player name
        YetkiliSayac.DrawTextShadow(stat.player_name, "YetkiliSayac_Text", x + 60, start_y + (i-1) * player_h + 10,
            YetkiliSayac.Config.Colors.black, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)

        -- Total time
        local total_time = YetkiliSayac.FormatTime(tonumber(stat.total_time) or 0)
        YetkiliSayac.DrawTextShadow(total_time, "YetkiliSayac_Small", x + w - 30, start_y + (i-1) * player_h + 10,
            YetkiliSayac.Config.Colors.primary, TEXT_ALIGN_RIGHT, TEXT_ALIGN_TOP)

        -- Sessions count
        local session_text = (tonumber(stat.total_sessions) or 0) .. " oturum"
        YetkiliSayac.DrawTextShadow(session_text, "YetkiliSayac_Small", x + 60, start_y + (i-1) * player_h + 25,
            YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
    end

    if #stats == 0 then
        YetkiliSayac.DrawTextShadow("Veri yükleniyor...", "YetkiliSayac_Text", x + w/2, start_y + 100,
            YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
end

-- Draw admin panel tab
function YetkiliSayac.Menu.DrawAdminPanelTab(x, y, w, h)
    if not YetkiliSayac.HasPermission(LocalPlayer(), "manage_system") then
        YetkiliSayac.DrawTextShadow("Bu panele erişim yetkiniz yok!", "YetkiliSayac_Header", x + w/2, y + h/2,
            YetkiliSayac.Config.Colors.danger, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        return
    end

    YetkiliSayac.DrawTextShadow("Yönetici Paneli", "YetkiliSayac_Header", x + 15, y + 20,
        YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)

    -- Admin features will be implemented here
    YetkiliSayac.DrawTextShadow("Yönetici özellikleri yakında eklenecek...", "YetkiliSayac_Text", x + w/2, y + h/2,
        YetkiliSayac.Config.Colors.dark, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
end

-- Main menu drawing function
function YetkiliSayac.Menu.Draw()
    if not YetkiliSayac.Menu.IsOpen then return end

    local scrw, scrh = ScrW(), ScrH()
    local menu_w, menu_h = 900, 600
    local menu_x, menu_y = (scrw - menu_w) / 2, (scrh - menu_h) / 2

    -- Animation
    local anim_time = CurTime() - YetkiliSayac.Menu.AnimationStart
    local anim_frac = math.min(anim_time / YetkiliSayac.Config.Menu.animation_speed, 1)
    local ease_frac = math.sin(anim_frac * math.pi * 0.5)

    -- Background blur
    if YetkiliSayac.Config.Menu.blur_background then
        Derma_DrawBackgroundBlur(menu_x, menu_y, menu_w, menu_h)
    end

    -- Dark overlay
    draw.RoundedBox(0, 0, 0, scrw, scrh, Color(0, 0, 0, 150 * ease_frac))

    -- Main menu background
    local final_y = menu_y + (1 - ease_frac) * 50
    YetkiliSayac.Menu.DrawGradientBox(menu_x, final_y, menu_w, menu_h,
        Color(255, 255, 255, 245 * ease_frac), Color(248, 249, 250, 245 * ease_frac), 12)

    -- Header
    local header_h = 80
    YetkiliSayac.DrawRoundedBox(12, menu_x, final_y, menu_w, header_h, YetkiliSayac.Config.Colors.primary)
    YetkiliSayac.DrawTextShadow(YetkiliSayac.Config.Language.menu_title, "YetkiliSayac_Title",
        menu_x + menu_w/2, final_y + header_h/2, YetkiliSayac.Config.Colors.white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

    -- Close button
    local close_size = 30
    local close_x = menu_x + menu_w - close_size - 15
    local close_y = final_y + 15

    local mx, my = gui.MouseX(), gui.MouseY()
    local close_hovered = mx >= close_x and mx <= close_x + close_size and my >= close_y and my <= close_y + close_size
    local close_color = close_hovered and Color(231, 76, 60) or Color(255, 255, 255, 100)

    YetkiliSayac.DrawRoundedBox(4, close_x, close_y, close_size, close_size, close_color)
    YetkiliSayac.DrawTextShadow("✕", "YetkiliSayac_Text", close_x + close_size/2, close_y + close_size/2,
        YetkiliSayac.Config.Colors.white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

    -- Tabs
    local tab_y = final_y + header_h + 20
    local tab_w = 200
    local tab_h = 40
    local tab_spacing = 10

    local tabs = {
        {name = YetkiliSayac.Config.Language.my_stats, icon = "👤"},
        {name = YetkiliSayac.Config.Language.all_sessions, icon = "📊"},
    }

    if YetkiliSayac.HasPermission(LocalPlayer(), "manage_system") then
        table.insert(tabs, {name = YetkiliSayac.Config.Language.admin_panel, icon = "⚙️"})
    end

    for i, tab in ipairs(tabs) do
        local tab_x = menu_x + 20
        local current_tab_y = tab_y + (i-1) * (tab_h + tab_spacing)

        local hovered = YetkiliSayac.Menu.DrawTabButton(tab_x, current_tab_y, tab_w, tab_h,
            tab.name, YetkiliSayac.Menu.CurrentTab == i, tab.icon)

        if hovered and input.IsMouseDown(MOUSE_LEFT) then
            YetkiliSayac.Menu.CurrentTab = i

            -- Request data for the selected tab
            if i == 2 and YetkiliSayac.HasPermission(LocalPlayer(), "view_all_stats") then
                net.Start("YetkiliSayac_RequestAllSessions")
                net.SendToServer()
            end
        end
    end

    -- Content area
    local content_x = menu_x + tab_w + 40
    local content_y = tab_y
    local content_w = menu_w - tab_w - 60
    local content_h = menu_h - header_h - 40

    -- Draw content based on current tab
    if YetkiliSayac.Menu.CurrentTab == 1 then
        YetkiliSayac.Menu.DrawMyStatsTab(content_x, content_y, content_w, content_h)
    elseif YetkiliSayac.Menu.CurrentTab == 2 then
        YetkiliSayac.Menu.DrawAllSessionsTab(content_x, content_y, content_w, content_h)
    elseif YetkiliSayac.Menu.CurrentTab == 3 then
        YetkiliSayac.Menu.DrawAdminPanelTab(content_x, content_y, content_w, content_h)
    end

    -- Handle close button click
    if close_hovered and input.IsMouseDown(MOUSE_LEFT) then
        YetkiliSayac.Menu.Close()
    end
end

-- Network receivers
net.Receive("YetkiliSayac_OpenMenu", function()
    print("[YetkiliSayac] Menü açma komutu alındı")
    YetkiliSayac.Menu.Open()
end)

net.Receive("YetkiliSayac_SendStats", function()
    YetkiliSayac.Menu.Data = net.ReadTable()
end)

net.Receive("YetkiliSayac_SendAllSessions", function()
    YetkiliSayac.Menu.Data.all_sessions = net.ReadTable()
end)

net.Receive("YetkiliSayac_SendAdminData", function()
    YetkiliSayac.Menu.Data.admin_data = net.ReadTable()
end)

-- Hooks
hook.Add("HUDPaint", "YetkiliSayac_MenuDraw", function()
    YetkiliSayac.Menu.Draw()
end)

hook.Add("Think", "YetkiliSayac_MenuThink", function()
    if YetkiliSayac.Menu.IsOpen then
        -- Close menu with ESC
        if input.IsKeyDown(KEY_ESCAPE) then
            YetkiliSayac.Menu.Close()
        end

        -- Prevent other menus from opening
        gui.EnableScreenClicker(true)
    end
end)

-- Key binding
hook.Add("PlayerButtonDown", "YetkiliSayac_MenuKey", function(ply, button)
    if ply ~= LocalPlayer() then return end

    if button == YetkiliSayac.Config.Menu.key then
        if YetkiliSayac.Menu.IsOpen then
            YetkiliSayac.Menu.Close()
        else
            YetkiliSayac.Menu.Open()
        end
    end
end)

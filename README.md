# Yetki<PERSON><PERSON>ayac - Professional Staff Time Tracker

Garry's Mod DarkRP için profesyonel yetkili sayaç sistemi. Yetkililerin görev sürelerini otomatik olarak takip eder, Discord entegrasyonu sağlar ve modern bir menü sistemi sunar.

## 🚀 Özellikler

### ⏱️ Otomatik Süre Takibi
- Yetkili mesleğe geçince otomatik sayaç başlatma
- Meslekten çıkınca otomatik sayaç durdurma
- Minimum oturum süresi kontrolü (5 dakika)
- AFK timeout desteği

### 📊 İstatistik Sistemi
- Toplam görev süresi
- Toplam oturum sayısı
- Son görev bilgisi
- Detaylı oturum geçmişi
- Günlük/haftalık raporlar

### 🎨 Modern Menü Sistemi
- **Benim İstatistiklerim**: Kişisel görev istatistikleri
- **Tüm Oturum<PERSON>**: Sunucudaki tüm yetkili istatistikleri
- **Yönetici Paneli**: SuperAdmin özel yönetim paneli
- Onyx/Xenin tarzı modern tasarım
- Animasyonlu geçişler
- Responsive tasarım

### 🔗 Discord Entegrasyonu
- Göreve başlama bildirimleri
- Görevden ayrılma bildirimleri
- Günlük aktivite raporları
- Haftalık en aktif yetkili raporları
- Özelleştirilebilir webhook mesajları

### 🛡️ Yetki Sistemi
- Kullanıcı: Kendi istatistiklerini görme
- Admin: Tüm istatistikleri görme
- SuperAdmin: Sistem yönetimi ve istatistik sıfırlama

## 📁 Dosya Yapısı

```
yetkisayacow/
├── addon.txt
├── README.md
└── lua/
    ├── autorun/server/
    │   └── sv_yetkili_sayac_init.lua
    └── yetkili_sayac/
        ├── shared/
        │   ├── sh_config.lua
        │   └── sh_functions.lua
        ├── server/
        │   ├── sv_core.lua
        │   ├── sv_database.lua
        │   └── sv_discord.lua
        └── client/
            └── cl_menu.lua
```

## ⚙️ Kurulum

1. Bu addon'u sunucunuzun `addons` klasörüne yerleştirin
2. `lua/yetkili_sayac/shared/sh_config.lua` dosyasını düzenleyin
3. Discord webhook URL'nizi ayarlayın
4. Yetkili mesleklerini yapılandırın
5. Sunucuyu yeniden başlatın

## 🔧 Yapılandırma

### Discord Webhook Ayarları
```lua
YetkiliSayac.Config.Discord = {
    enabled = true,
    webhook_url = "YOUR_DISCORD_WEBHOOK_URL_HERE", -- Discord webhook URL'nizi buraya girin
    bot_name = "YetkiliSayac Bot",
    avatar_url = "https://i.imgur.com/your_avatar.png",
    color = 3447003, -- Mavi renk
    mention_role = "", -- Mention edilecek rol ID'si (opsiyonel)
}
```

### Yetkili Meslekleri
```lua
YetkiliSayac.Config.StaffJobs = {
    ["Moderator"] = {
        enabled = true,
        min_rank = "moderator",
        color = Color(0, 255, 0),
        discord_color = 65280, -- Yeşil
    },
    ["Admin"] = {
        enabled = true,
        min_rank = "admin",
        color = Color(255, 165, 0),
        discord_color = 16753920, -- Turuncu
    },
    ["SuperAdmin"] = {
        enabled = true,
        min_rank = "superadmin",
        color = Color(255, 0, 0),
        discord_color = 16711680, -- Kırmızı
    },
}
```

## 🎮 Kullanım

### Komutlar
- `!yetkilimenu` - Yetkili menüsünü açar
- `yetkilimenu` - Konsol komutu
- `F4` tuşu - Menü açma (yapılandırılabilir)

### Menü Sekmeleri

#### 1. Benim İstatistiklerim
- Toplam görev süresi
- Toplam oturum sayısı
- Son görev bilgisi
- Aktif oturum durumu
- Son oturumlar listesi

#### 2. Tüm Oturumlar
- En aktif yetkililerin listesi
- Toplam süre sıralaması
- Oturum sayısı bilgileri

#### 3. Yönetici Paneli (SuperAdmin)
- Oyuncu istatistiklerini görüntüleme
- İstatistik sıfırlama
- Sistem yönetimi

## 🗄️ Veritabanı

Sistem SQLite veritabanı kullanır ve iki ana tablo oluşturur:

### Sessions Tablosu
- Tüm yetkili oturumlarını saklar
- Başlangıç/bitiş zamanları
- Süre bilgileri
- Ayrılma sebepleri

### Stats Tablosu
- Oyuncu istatistiklerini saklar
- Toplam süreler
- Oturum sayıları
- Son aktivite bilgileri

## 🔄 Otomatik İşlemler

### Günlük Temizlik
- Eski kayıtları otomatik temizler (365 gün)
- Performansı optimize eder

### Raporlama
- Günlük aktivite raporları
- Haftalık en aktif yetkili raporları
- Discord'a otomatik gönderim

## 🛠️ Geliştirici Notları

### Hook'lar
- `playerChangedJob` - Meslek değişikliği takibi
- `PlayerInitialSpawn` - Oyuncu bağlantısı
- `PlayerDisconnected` - Oyuncu ayrılması

### Network Strings
- `YetkiliSayac_OpenMenu`
- `YetkiliSayac_RequestStats`
- `YetkiliSayac_SendStats`
- `YetkiliSayac_RequestAllSessions`
- `YetkiliSayac_SendAllSessions`
- `YetkiliSayac_RequestAdminData`
- `YetkiliSayac_SendAdminData`
- `YetkiliSayac_ResetPlayerStats`

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Bu repository'yi fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📞 Destek

Herhangi bir sorun yaşarsanız veya öneriniz varsa lütfen issue oluşturun.

---

**YetkiliSayac** - Professional Staff Time Tracker for Garry's Mod DarkRP

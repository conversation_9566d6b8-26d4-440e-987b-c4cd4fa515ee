--[[
    Yet<PERSON><PERSON><PERSON><PERSON><PERSON> - Professional Staff Time Tracker
    Shared Functions
]]--

YetkiliSayac = YetkiliSayac or {}

-- Time formatting function
function YetkiliSayac.FormatTime(seconds)
    if not seconds or seconds <= 0 then return "0 dakika" end
    
    local days = math.floor(seconds / 86400)
    local hours = math.floor((seconds % 86400) / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    
    local parts = {}
    
    if days > 0 then
        table.insert(parts, days .. " gün")
    end
    
    if hours > 0 then
        table.insert(parts, hours .. " saat")
    end
    
    if minutes > 0 then
        table.insert(parts, minutes .. " dakika")
    end
    
    if #parts == 0 then
        return "1 dakika"
    end
    
    return table.concat(parts, " ")
end

-- Check if player has permission
function YetkiliSayac.HasPermission(ply, permission)
    if not IsValid(ply) then return false end
    
    local required_rank = YetkiliSayac.Config.Permissions[permission]
    if not required_rank then return false end
    
    if required_rank == "user" then return true end
    if required_rank == "admin" and ply:IsAdmin() then return true end
    if required_rank == "superadmin" and ply:IsSuperAdmin() then return true end
    if required_rank == "moderator" and (ply:IsAdmin() or ply:IsSuperAdmin()) then return true end
    
    return false
end

-- Check if job is a staff job
function YetkiliSayac.IsStaffJob(job_name)
    if not job_name then return false end
    
    for staff_job, config in pairs(YetkiliSayac.Config.StaffJobs) do
        if string.lower(job_name) == string.lower(staff_job) and config.enabled then
            return true, config
        end
    end
    
    return false
end

-- Get player's current job
function YetkiliSayac.GetPlayerJob(ply)
    if not IsValid(ply) then return nil end

    -- Try DarkRP method first
    if ply.getDarkRPVar then
        local job = ply:getDarkRPVar("job")
        if job then return job end
    end

    -- Try team name method
    if ply:Team() then
        local team_name = team.GetName(ply:Team())
        if team_name and team_name ~= "" then
            return team_name
        end
    end

    -- Try class name method
    if ply.GetPlayerClass then
        local class = ply:GetPlayerClass()
        if class and class ~= "" then
            return class
        end
    end

    return "Citizen"
end

-- Network string registration
if SERVER then
    util.AddNetworkString("YetkiliSayac_OpenMenu")
    util.AddNetworkString("YetkiliSayac_RequestStats")
    util.AddNetworkString("YetkiliSayac_SendStats")
    util.AddNetworkString("YetkiliSayac_RequestAllSessions")
    util.AddNetworkString("YetkiliSayac_SendAllSessions")
    util.AddNetworkString("YetkiliSayac_RequestAdminData")
    util.AddNetworkString("YetkiliSayac_SendAdminData")
    util.AddNetworkString("YetkiliSayac_ResetPlayerStats")
    util.AddNetworkString("YetkiliSayac_UpdateNotification")
end

-- Logging function
function YetkiliSayac.Log(message, level)
    level = level or "INFO"
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    print(string.format("[YetkiliSayac][%s][%s] %s", timestamp, level, message))
end

-- Color lerp function for animations
function YetkiliSayac.LerpColor(frac, from, to)
    return Color(
        Lerp(frac, from.r, to.r),
        Lerp(frac, from.g, to.g),
        Lerp(frac, from.b, to.b),
        Lerp(frac, from.a or 255, to.a or 255)
    )
end

-- Draw rounded rectangle
function YetkiliSayac.DrawRoundedBox(radius, x, y, w, h, color)
    draw.RoundedBox(radius, x, y, w, h, color)
end

-- Draw text with shadow
function YetkiliSayac.DrawTextShadow(text, font, x, y, color, xalign, yalign, shadow_offset, shadow_color)
    shadow_offset = shadow_offset or 1
    shadow_color = shadow_color or Color(0, 0, 0, 100)
    
    -- Draw shadow
    draw.SimpleText(text, font, x + shadow_offset, y + shadow_offset, shadow_color, xalign, yalign)
    
    -- Draw main text
    draw.SimpleText(text, font, x, y, color, xalign, yalign)
end

-- Validate SteamID
function YetkiliSayac.IsValidSteamID(steamid)
    if not steamid or type(steamid) ~= "string" then return false end
    return string.match(steamid, "^STEAM_[0-5]:[01]:[0-9]+$") ~= nil
end

-- Get player by SteamID
function YetkiliSayac.GetPlayerBySteamID(steamid)
    if not YetkiliSayac.IsValidSteamID(steamid) then return nil end
    
    for _, ply in ipairs(player.GetAll()) do
        if ply:SteamID() == steamid then
            return ply
        end
    end
    
    return nil
end

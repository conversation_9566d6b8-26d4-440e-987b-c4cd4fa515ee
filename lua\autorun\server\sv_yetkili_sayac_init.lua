--[[
    Yet<PERSON><PERSON><PERSON><PERSON><PERSON> - Professional Staff Time Tracker
    Server Initialization
]]--

-- Load shared files first
include("yetkili_sayac/shared/sh_config.lua")
include("yetkili_sayac/shared/sh_functions.lua")

-- Send to client
AddCSLuaFile("yetkili_sayac/shared/sh_config.lua")
AddCSLuaFile("yetkili_sayac/shared/sh_functions.lua")
AddCSLuaFile("yetkili_sayac/client/cl_menu.lua")

-- Load server files
include("yetkili_sayac/server/sv_database.lua")
include("yetkili_sayac/server/sv_discord.lua")
include("yetkili_sayac/server/sv_core.lua")

-- Initialize system when server starts
hook.Add("Initialize", "YetkiliSayac_Initialize", function()
    -- Wait a bit for all files to load
    timer.Simple(1, function()
        if YetkiliSayac and YetkiliSayac.Log then
            YetkiliSayac.Log("YetkiliSayac sistemi başlatılıyor...")

            -- Initialize database
            if YetkiliSayac.Database and YetkiliSayac.Database.Initialize then
                YetkiliSayac.Database.Initialize()
            end

            YetkiliSayac.Log("YetkiliSayac sistemi başarıyla başlatıldı!")
        else
            print("[YetkiliSayac] HATA: Sistem yüklenemedi!")
        end
    end)
end)

-- Player ready hook
hook.Add("PlayerInitialSpawn", "YetkiliSayac_PlayerReady", function(ply)
    timer.Simple(5, function()
        if IsValid(ply) and YetkiliSayac and YetkiliSayac.Log then
            YetkiliSayac.Log(ply:Nick() .. " (" .. ply:SteamID() .. ") sunucuya katıldı")

            -- Check if player is already in a staff job
            if YetkiliSayac.GetPlayerJob then
                local job = YetkiliSayac.GetPlayerJob(ply)
                local isStaff, config = YetkiliSayac.IsStaffJob(job)

                if isStaff and YetkiliSayac.StartSession then
                    YetkiliSayac.StartSession(ply, job)
                end
            end
        end
    end)
end)

-- Player disconnect hook
hook.Add("PlayerDisconnected", "YetkiliSayac_PlayerDisconnect", function(ply)
    if IsValid(ply) and YetkiliSayac and YetkiliSayac.Log then
        YetkiliSayac.Log(ply:Nick() .. " (" .. ply:SteamID() .. ") sunucudan ayrıldı")
        if YetkiliSayac.EndSession then
            YetkiliSayac.EndSession(ply, "Sunucudan ayrıldı")
        end
    end
end)

-- Console command for menu
concommand.Add("yetkilimenu", function(ply, cmd, args)
    if not IsValid(ply) then return end

    if YetkiliSayac and YetkiliSayac.HasPermission and YetkiliSayac.HasPermission(ply, "view_own_stats") then
        net.Start("YetkiliSayac_OpenMenu")
        net.Send(ply)
        if YetkiliSayac.Log then
            YetkiliSayac.Log(ply:Nick() .. " menüyü açtı")
        end
    else
        ply:ChatPrint("[YetkiliSayac] Bu komutu kullanma yetkiniz yok!")
    end
end)

-- Test commands for debugging
concommand.Add("yetkili_test_start", function(ply, cmd, args)
    if not IsValid(ply) then return end
    if not ply:IsSuperAdmin() then return end

    local job = args[1] or "Admin"
    YetkiliSayac.StartSession(ply, job)
    ply:ChatPrint("[YetkiliSayac] Test oturumu başlatıldı: " .. job)
end)

concommand.Add("yetkili_test_end", function(ply, cmd, args)
    if not IsValid(ply) then return end
    if not ply:IsSuperAdmin() then return end

    YetkiliSayac.EndSession(ply, "Test bitişi")
    ply:ChatPrint("[YetkiliSayac] Test oturumu sonlandırıldı")
end)

concommand.Add("yetkili_test_discord", function(ply, cmd, args)
    if not IsValid(ply) then return end
    if not ply:IsSuperAdmin() then return end

    YetkiliSayac.Discord.SendJobStart(ply, "Test Admin")
    ply:ChatPrint("[YetkiliSayac] Test Discord mesajı gönderildi")
end)

-- Reset database command
concommand.Add("yetkili_reset_db", function(ply, cmd, args)
    if not IsValid(ply) then return end
    if not ply:IsSuperAdmin() then return end

    if YetkiliSayac and YetkiliSayac.Database and YetkiliSayac.Database.Initialize then
        YetkiliSayac.Database.Initialize()
        ply:ChatPrint("[YetkiliSayac] Veritabanı yeniden oluşturuldu!")
    end
end)

-- Check database tables
concommand.Add("yetkili_check_db", function(ply, cmd, args)
    if not IsValid(ply) then return end
    if not ply:IsSuperAdmin() then return end

    local prefix = YetkiliSayac.Config.Database.table_prefix

    -- Check sessions table
    local sessions_check = sql.Query(string.format("SELECT name FROM sqlite_master WHERE type='table' AND name='%ssessions'", prefix))
    if sessions_check and #sessions_check > 0 then
        ply:ChatPrint("[YetkiliSayac] Sessions tablosu mevcut")

        -- Check columns
        local columns = sql.Query(string.format("PRAGMA table_info(%ssessions)", prefix))
        if columns then
            ply:ChatPrint("[YetkiliSayac] Sessions tablo kolonları:")
            for _, col in ipairs(columns) do
                ply:ChatPrint("  - " .. col.name .. " (" .. col.type .. ")")
            end
        end
    else
        ply:ChatPrint("[YetkiliSayac] Sessions tablosu bulunamadı!")
    end

    -- Check stats table
    local stats_check = sql.Query(string.format("SELECT name FROM sqlite_master WHERE type='table' AND name='%sstats'", prefix))
    if stats_check and #stats_check > 0 then
        ply:ChatPrint("[YetkiliSayac] Stats tablosu mevcut")
    else
        ply:ChatPrint("[YetkiliSayac] Stats tablosu bulunamadı!")
    end
end)

-- Chat command hook
hook.Add("PlayerSay", "YetkiliSayac_ChatCommand", function(ply, text, team)
    if YetkiliSayac and YetkiliSayac.Config and YetkiliSayac.Config.Menu then
        if string.lower(text) == "!" .. YetkiliSayac.Config.Menu.command then
            if YetkiliSayac.HasPermission and YetkiliSayac.HasPermission(ply, "view_own_stats") then
                net.Start("YetkiliSayac_OpenMenu")
                net.Send(ply)
            else
                ply:ChatPrint("[YetkiliSayac] Bu komutu kullanma yetkiniz yok!")
            end
            return ""
        end
    end
end)

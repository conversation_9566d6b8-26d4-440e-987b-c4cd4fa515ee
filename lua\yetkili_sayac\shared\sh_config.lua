--[[
    Yet<PERSON><PERSON><PERSON>ayac - Professional Staff Time Tracker
    Configuration File
]]--

YetkiliSayac = YetkiliSayac or {}
YetkiliSayac.Config = {}

-- Discord Webhook Configuration
YetkiliSayac.Config.Discord = {
    enabled = true,
    webhook_url = "https://discord.com/api/webhooks/1381917480762740797/IWfyOg3axFjrDQe3siQ_T4L85qieIk2_sR3_mno8mFscUBl2ck1Snqo9uRbaUYcUar68", -- Discord webhook URL'nizi buraya girin
    bot_name = "YetkiliSayac Bot",
    avatar_url = "https://i.imgur.com/your_avatar.png",
    color = 3447003, -- <PERSON>vi renk
    mention_role = "", -- Mention edilecek rol ID'si (opsiyonel)
}

-- Staff Jobs Configuration
YetkiliSayac.Config.StaffJobs = {
    ["Moderator"] = {
        enabled = true,
        min_rank = "moderator",
        color = Color(0, 255, 0),
        discord_color = 65280, -- <PERSON><PERSON><PERSON>
    },
    ["Admin"] = {
        enabled = true,
        min_rank = "admin",
        color = Color(255, 165, 0),
        discord_color = 16753920, -- Turuncu
    },
    ["SuperAdmin"] = {
        enabled = true,
        min_rank = "superadmin",
        color = Color(255, 0, 0),
        discord_color = 16711680, -- Kırmızı
    },
    -- DarkRP varsayılan yetkili meslekleri
    ["Hobo"] = {
        enabled = true,
        min_rank = "user",
        color = Color(0, 100, 255),
        discord_color = 255, -- Mavi
    },
    ["Police Officer"] = {
        enabled = true,
        min_rank = "user",
        color = Color(0, 100, 255),
        discord_color = 255, -- Mavi
    },
}

-- Menu Configuration
YetkiliSayac.Config.Menu = {
    command = "yetkilimenu",
    key = KEY_F4, -- Menü açma tuşu (opsiyonel)
    modern_style = true,
    blur_background = true,
    animation_speed = 0.3,
}

-- Database Configuration
YetkiliSayac.Config.Database = {
    type = "sqlite", -- sqlite veya mysql
    table_prefix = "yetkili_sayac_",
    auto_cleanup_days = 365, -- Eski kayıtları temizleme süresi (gün)
}

-- Time Configuration
YetkiliSayac.Config.Time = {
    update_interval = 60, -- Saniye cinsinden güncelleme aralığı
    min_session_time = 300, -- Minimum oturum süresi (5 dakika)
    afk_timeout = 600, -- AFK timeout süresi (10 dakika)
}

-- Permissions
YetkiliSayac.Config.Permissions = {
    view_own_stats = "user", -- Kendi istatistiklerini görme
    view_all_stats = "admin", -- Tüm istatistikleri görme
    manage_system = "superadmin", -- Sistemi yönetme
    reset_stats = "superadmin", -- İstatistikleri sıfırlama
}

-- Language Configuration
YetkiliSayac.Config.Language = {
    menu_title = "Yetkili Sayaç Sistemi",
    my_stats = "Benim İstatistiklerim",
    all_sessions = "Tüm Oturumlar",
    admin_panel = "Yönetici Paneli",
    total_time = "Toplam Süre",
    current_session = "Mevcut Oturum",
    last_session = "Son Oturum",
    sessions_count = "Oturum Sayısı",
    average_session = "Ortalama Oturum",
    job_started = "Göreve Başladı",
    job_ended = "Görevden Ayrıldı",
    time_format = "%d gün %d saat %d dakika",
    no_data = "Veri bulunamadı",
    loading = "Yükleniyor...",
}

-- Colors
YetkiliSayac.Config.Colors = {
    primary = Color(52, 152, 219),
    secondary = Color(155, 89, 182),
    success = Color(46, 204, 113),
    warning = Color(241, 196, 15),
    danger = Color(231, 76, 60),
    dark = Color(52, 73, 94),
    light = Color(236, 240, 241),
    white = Color(255, 255, 255),
    black = Color(0, 0, 0),
}
